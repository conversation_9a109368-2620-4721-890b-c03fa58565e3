# Course Preview Modal - Versão Simplificada

## Descrição

Modal de preview de curso simplificado e fiel à imagem de referência, com integração dinâmica à API.

## Características

### ✅ Implementadas

- **Layout vertical** idêntico à imagem
- **Branding Xacquisition360** no topo
- **Player de vídeo** com botão "Retomar"
- **Lista de aulas** simplificada
- **Integração com API** dinâmica
- **Fallback para dados mock** se API falhar
- **Loading states** no botão
- **Tratamento de erros**

### 🎨 Design

- **Tema dark** com gradientes
- **Logo Xacquisition360** em azul
- **Layout vertical** responsivo
- **Animações suaves** com Framer Motion
- **Interface limpa** e moderna

## Como Usar

### 1. Importar o componente

```tsx
import { CoursePreviewModal } from "./CoursePreviewModal";
```

### 2. Usar no componente

```tsx
const [isModalOpen, setIsModalOpen] = useState(false);
const [selectedCourse, setSelectedCourse] = useState(null);
const [previewLoading, setPreviewLoading] = useState(false);

const handleOpenModal = async (courseId: string) => {
  setPreviewLoading(true);

  try {
    const response = await fetch(`/api/courses/${courseId}/preview?organizationSlug=${organizationSlug}`);
    const courseData = await response.json();
    setSelectedCourse(courseData);
    setIsModalOpen(true);
  } catch (error) {
    // Fallback para dados mock
    const { courseData } = useMockCoursePreview(courseId);
    setSelectedCourse(courseData);
    setIsModalOpen(true);
  } finally {
    setPreviewLoading(false);
  }
};

// No JSX
{selectedCourse && (
  <CoursePreviewModal
    isOpen={isModalOpen}
    onClose={() => {
      setIsModalOpen(false);
      setSelectedCourse(null);
    }}
    course={selectedCourse}
    onPlayLesson={(lessonId) => {
      console.log("Playing lesson:", lessonId);
    }}
  />
)}
```

## Estrutura de Dados

### CoursePreviewData

```typescript
interface CoursePreviewData {
  id: string;
  title: string;
  description?: string;
  logo?: string;
  modules: Module[];
  firstLesson?: Lesson;
}
```

### Module

```typescript
interface Module {
  id: string;
  title: string;
  lessons: Lesson[];
}
```

### Lesson

```typescript
interface Lesson {
  id: string;
  title: string;
  duration: string;
  thumbnail?: string;
  isCompleted?: boolean;
  videoUrl?: string;
}
```

## API Integration

### Endpoint

```typescript
GET /api/courses/:courseId/preview?organizationSlug=:slug
```

### Resposta Esperada

```json
{
  "id": "course-id",
  "title": "Intensivo Microprodutos",
  "description": "Aprenda a criar e vender microprodutos",
  "logo": "url-do-logo",
  "modules": [
    {
      "id": "module-1",
      "title": "Microprodutos",
      "lessons": [
        {
          "id": "lesson-1",
          "title": "Intensivo Microprodutos",
          "duration": "28 min",
          "isCompleted": false,
          "videoUrl": "https://example.com/video.mp4"
        }
      ]
    }
  ],
  "firstLesson": {
    "id": "lesson-1",
    "title": "Intensivo Microprodutos",
    "duration": "28 min",
    "videoUrl": "https://example.com/video.mp4"
  }
}
```

## Funcionalidades

### Video Player
- Preview da primeira aula
- Botão "Retomar" para continuar
- Thumbnail com logo Xacquisition360

### Lista de Aulas
- Checkbox de conclusão
- Thumbnail da aula
- Título e duração
- Clique para reproduzir

### Módulos
- Seletor de módulo (dropdown)
- Contagem de módulos e aulas
- Aulas do módulo selecionado

## Performance

- **React.memo** para evitar re-renders
- **useMemo** para cálculos pesados
- **useCallback** para funções
- **Loading states** para UX
- **Error handling** robusto

## Customização

### Cores

```css
/* Cores principais */
--brand-blue: #3b82f6; /* Xacquisition360 */
--background-dark: #111827;
--background-darker: #1f2937;
--text-white: #ffffff;
--text-gray: #9ca3af;
```

### Branding

Para alterar o branding, edite:

```tsx
<div className="text-white font-bold text-lg">
  <span className="text-blue-400">X</span>acquisition360
</div>
```

## Arquivos Relacionados

- `CoursePreviewModal.tsx` - Modal principal
- `useCoursePreview.ts` - Hooks para dados
- `types/course-preview.ts` - Tipos TypeScript
- `get-course-preview.ts` - Endpoint da API
