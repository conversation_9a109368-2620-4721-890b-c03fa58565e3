"use client";

import { useState, useEffect } from "react";
import { useSession } from "@saas/auth/hooks/use-session";
import { useActiveOrganization } from "@saas/organizations/hooks/use-active-organization";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Button } from "@ui/components/button";
import { Skeleton } from "@ui/components/skeleton";
import {
	PlayIcon,
	BookOpenIcon,
	VideoIcon,
	UsersIcon,
	ClockIcon,
	StarIcon,
	ArrowRightIcon,
	GraduationCapIcon,
	MessageSquareIcon,
	HelpCircleIcon,
} from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

interface ContentItem {
	id: string;
	title: string;
	description: string;
	type: "course" | "mentoring" | "video";
	image?: string;
	progress?: number;
	duration?: string;
	studentsCount?: number;
	rating?: number;
	isAccessible: boolean;
	organizationSlug?: string;
}

interface StudentDashboardProps {
	organizationSlug?: string;
}

export function StudentDashboard({ organizationSlug }: StudentDashboardProps) {
	const { user } = useSession();
	const { activeOrganization } = useActiveOrganization();
	const router = useRouter();
	const [contentItems, setContentItems] = useState<ContentItem[]>([]);
	const [isLoading, setIsLoading] = useState(true);

	// Mock data - em produção, isso viria de uma API
	useEffect(() => {
		const mockContent: ContentItem[] = [
			{
				id: "1",
				title: "Fundamentos de Marketing Digital",
				description: "Aprenda os conceitos básicos do marketing digital e como aplicá-los no seu negócio.",
				type: "course",
				image: "/images/courses/marketing.jpg",
				progress: 65,
				duration: "4h 30min",
				studentsCount: 1250,
				rating: 4.8,
				isAccessible: true,
				organizationSlug: organizationSlug,
			},
			{
				id: "2",
				title: "Sessão de Mentoria: Estratégias de Vendas",
				description: "Mentoria individual para desenvolver suas habilidades de vendas.",
				type: "mentoring",
				duration: "1h",
				rating: 4.9,
				isAccessible: true,
				organizationSlug: organizationSlug,
			},
			{
				id: "3",
				title: "Como Criar Conteúdo Viral",
				description: "Técnicas avançadas para criar conteúdo que engaja e viraliza nas redes sociais.",
				type: "video",
				image: "/images/videos/viral-content.jpg",
				duration: "45min",
				studentsCount: 890,
				rating: 4.7,
				isAccessible: true,
				organizationSlug: organizationSlug,
			},
			{
				id: "4",
				title: "Curso Avançado de E-commerce",
				description: "Estratégias avançadas para escalar seu e-commerce e aumentar as vendas.",
				type: "course",
				image: "/images/courses/ecommerce.jpg",
				duration: "8h 15min",
				studentsCount: 2100,
				rating: 4.9,
				isAccessible: false, // Exemplo de conteúdo não acessível
				organizationSlug: organizationSlug,
			},
		];

		// Simular carregamento
		setTimeout(() => {
			setContentItems(mockContent);
			setIsLoading(false);
		}, 1000);
	}, [organizationSlug]);

	const getContentIcon = (type: ContentItem["type"]) => {
		switch (type) {
			case "course":
				return BookOpenIcon;
			case "mentoring":
				return UsersIcon;
			case "video":
				return VideoIcon;
			default:
				return PlayIcon;
		}
	};

	const getContentTypeLabel = (type: ContentItem["type"]) => {
		switch (type) {
			case "course":
				return "Curso";
			case "mentoring":
				return "Mentoria";
			case "video":
				return "Vídeo";
			default:
				return "Conteúdo";
		}
	};

	const handleContentClick = (item: ContentItem) => {
		if (!item.isAccessible) {
			// Redirecionar para página de upgrade ou mostrar modal
			return;
		}

		// Redirecionar para o conteúdo específico
		if (item.organizationSlug) {
			router.push(`/app/${item.organizationSlug}/${item.type}/${item.id}`);
		}
	};

	const accessibleContent = contentItems.filter(item => item.isAccessible);
	const lockedContent = contentItems.filter(item => !item.isAccessible);

	if (isLoading) {
		return (
			<div className="space-y-8">
				<div className="space-y-4">
					<Skeleton className="h-8 w-64" />
					<Skeleton className="h-4 w-96" />
				</div>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{[...Array(6)].map((_, i) => (
						<Card key={i} className="overflow-hidden">
							<Skeleton className="h-48 w-full" />
							<CardContent className="p-6 space-y-3">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-3 w-3/4" />
								<div className="flex justify-between">
									<Skeleton className="h-3 w-16" />
									<Skeleton className="h-3 w-20" />
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-8">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold text-foreground">
						Olá, {user?.name}! 👋
					</h1>
					<p className="text-muted-foreground mt-2">
						Continue seu aprendizado e explore novos conteúdos.
					</p>
				</div>
				 
			</div>

		 

	 
			{accessibleContent.length > 0 && (
				<div className="space-y-6">
					<div className="flex items-center justify-between">
						<h2 className="text-2xl font-bold text-foreground">
							Seus Conteúdos
						</h2>
						<Button variant="ghost" size="sm">
							Ver todos
							<ArrowRightIcon className="h-4 w-4 ml-2" />
						</Button>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{accessibleContent.map((item, index) => {
							const IconComponent = getContentIcon(item.type);
							return (
								<motion.div
									key={item.id}
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: index * 0.1 }}
								>
									<Card
										className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group"
										onClick={() => handleContentClick(item)}
									>
										{item.image && (
											<div className="relative h-48 bg-gradient-to-br from-blue-500 to-purple-600">
												<div className="absolute inset-0 bg-black/20" />
												<div className="absolute top-4 left-4">
													<Badge className="bg-white/90 text-gray-800">
														{getContentTypeLabel(item.type)}
													</Badge>
												</div>
												<div className="absolute bottom-4 left-4 right-4">
													<div className="flex items-center justify-between text-white">
														<div className="flex items-center space-x-2">
															<IconComponent className="h-5 w-5" />
															{item.duration && (
																<span className="text-sm flex items-center">
																	<ClockIcon className="h-4 w-4 mr-1" />
																	{item.duration}
																</span>
															)}
														</div>
														{item.rating && (
															<div className="flex items-center space-x-1">
																<StarIcon className="h-4 w-4 fill-yellow-400 text-yellow-400" />
																<span className="text-sm">{item.rating}</span>
															</div>
														)}
													</div>
												</div>
											</div>
										)}

										<CardContent className="p-6">
											<div className="space-y-3">
												<div className="flex items-start justify-between">
													<h3 className="font-semibold text-lg line-clamp-2 group-hover:text-primary transition-colors">
														{item.title}
													</h3>
												</div>

												<p className="text-muted-foreground text-sm line-clamp-2">
													{item.description}
												</p>

												{item.progress !== undefined && (
													<div className="space-y-2">
														<div className="flex justify-between text-sm">
															<span className="text-muted-foreground">Progresso</span>
															<span className="font-medium">{item.progress}%</span>
														</div>
														<div className="w-full bg-gray-200 rounded-full h-2">
															<div
																className="bg-primary h-2 rounded-full transition-all duration-300"
																style={{ width: `${item.progress}%` }}
															/>
														</div>
													</div>
												)}

												<div className="flex items-center justify-between pt-2">
													{item.studentsCount && (
														<div className="flex items-center text-sm text-muted-foreground">
															<UsersIcon className="h-4 w-4 mr-1" />
															{item.studentsCount.toLocaleString()} alunos
														</div>
													)}

													<Button size="sm" className="group-hover:bg-primary group-hover:text-white transition-colors">
														{item.progress ? 'Continuar' : 'Começar'}
														<PlayIcon className="h-4 w-4 ml-2" />
													</Button>
												</div>
											</div>
										</CardContent>
									</Card>
								</motion.div>
							);
						})}
					</div>
				</div>
			)}

			{/* Conteúdo Bloqueado */}
			{lockedContent.length > 0 && (
				<div className="space-y-6">
					<div className="flex items-center justify-between">
						<h2 className="text-2xl font-bold text-foreground">
							Conteúdo Premium
						</h2>
						<Button variant="outline" size="sm">
							Fazer Upgrade
							<ArrowRightIcon className="h-4 w-4 ml-2" />
						</Button>
					</div>

					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{lockedContent.map((item, index) => {
							const IconComponent = getContentIcon(item.type);
							return (
								<motion.div
									key={item.id}
									initial={{ opacity: 0, y: 20 }}
									animate={{ opacity: 1, y: 0 }}
									transition={{ delay: (accessibleContent.length + index) * 0.1 }}
								>
									<Card className="overflow-hidden opacity-75 relative">
										<div className="absolute inset-0 bg-black/10 z-10" />
										<div className="absolute top-4 right-4 z-20">
											<Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
												Premium
											</Badge>
										</div>

										{item.image && (
											<div className="relative h-48 bg-gradient-to-br from-gray-400 to-gray-600">
												<div className="absolute top-4 left-4">
													<Badge className="bg-white/90 text-gray-800">
														{getContentTypeLabel(item.type)}
													</Badge>
												</div>
												<div className="absolute bottom-4 left-4 right-4">
													<div className="flex items-center justify-between text-white">
														<div className="flex items-center space-x-2">
															<IconComponent className="h-5 w-5" />
															{item.duration && (
																<span className="text-sm flex items-center">
																	<ClockIcon className="h-4 w-4 mr-1" />
																	{item.duration}
																</span>
															)}
														</div>
														{item.rating && (
															<div className="flex items-center space-x-1">
																<StarIcon className="h-4 w-4 fill-yellow-400 text-yellow-400" />
																<span className="text-sm">{item.rating}</span>
															</div>
														)}
													</div>
												</div>
											</div>
										)}

										<CardContent className="p-6">
											<div className="space-y-3">
												<h3 className="font-semibold text-lg line-clamp-2">
													{item.title}
												</h3>

												<p className="text-muted-foreground text-sm line-clamp-2">
													{item.description}
												</p>

												<div className="flex items-center justify-between pt-2">
													{item.studentsCount && (
														<div className="flex items-center text-sm text-muted-foreground">
															<UsersIcon className="h-4 w-4 mr-1" />
															{item.studentsCount.toLocaleString()} alunos
														</div>
													)}

													<Button size="sm" variant="outline" disabled>
														Bloqueado
													</Button>
												</div>
											</div>
										</CardContent>
									</Card>
								</motion.div>
							);
						})}
					</div>
				</div>
			)}
		</div>
	);
}
