"use client";

import { useState, useEffect } from "react";
import { useSession } from "@saas/auth/hooks/use-session";
import { Card, CardContent } from "@ui/components/card";
import { Badge } from "@ui/components/badge";
import { Skeleton } from "@ui/components/skeleton";
import { BookOpenIcon, GraduationCapIcon } from "lucide-react";
import { motion } from "framer-motion";
import { useRouter } from "next/navigation";

interface ContentItem {
	id: string;
	title: string;
	description: string;
	type: "course" | "mentoring" | "video";
	image?: string;
	isAccessible: boolean;
	organizationSlug?: string;
}

interface StudentDashboardProps {
	organizationSlug?: string;
}

export function StudentDashboard({ organizationSlug }: StudentDashboardProps) {
	const { user } = useSession();
	const router = useRouter();
	const [contentItems, setContentItems] = useState<ContentItem[]>([]);
	const [isLoading, setIsLoading] = useState(true);

	// Mock data - em produção, isso viria de uma API
	useEffect(() => {
		const mockContent: ContentItem[] = [
			{
				id: "1",
				title: "Fundamentos de Marketing Digital",
				description: "Aprenda os conceitos básicos do marketing digital e como aplicá-los no seu negócio.",
				type: "course",
				image: "/images/courses/marketing.jpg",
				isAccessible: true,
				organizationSlug: organizationSlug,
			},
			{
				id: "2",
				title: "Sessão de Mentoria: Estratégias de Vendas",
				description: "Mentoria individual para desenvolver suas habilidades de vendas.",
				type: "mentoring",
				isAccessible: true,
				organizationSlug: organizationSlug,
			},
			{
				id: "3",
				title: "Como Criar Conteúdo Viral",
				description: "Técnicas avançadas para criar conteúdo que engaja e viraliza nas redes sociais.",
				type: "video",
				image: "/images/videos/viral-content.jpg",
				isAccessible: true,
				organizationSlug: organizationSlug,
			},
			{
				id: "4",
				title: "Curso Avançado de E-commerce",
				description: "Estratégias avançadas para escalar seu e-commerce e aumentar as vendas.",
				type: "course",
				image: "/images/courses/ecommerce.jpg",
				isAccessible: false, // Exemplo de conteúdo não acessível
				organizationSlug: organizationSlug,
			},
		];

		// Simular carregamento
		setTimeout(() => {
			setContentItems(mockContent);
			setIsLoading(false);
		}, 1000);
	}, [organizationSlug]);

	const handleContentClick = (item: ContentItem) => {
		if (!item.isAccessible) {
			return;
		}

		// Redirecionar para o curso como aluno
		if (item.organizationSlug) {
			router.push(`/app/${item.organizationSlug}/course/${item.id}`);
		}
	};

	const accessibleContent = contentItems.filter(item => item.isAccessible);

	if (isLoading) {
		return (
			<div className="space-y-8">
				<div className="space-y-4">
					<Skeleton className="h-8 w-64" />
					<Skeleton className="h-4 w-96" />
				</div>
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{[...Array(6)].map((_, i) => (
						<Card key={i} className="overflow-hidden">
							<Skeleton className="h-48 w-full" />
							<CardContent className="p-6 space-y-3">
								<Skeleton className="h-4 w-full" />
								<Skeleton className="h-3 w-3/4" />
								<div className="flex justify-between">
									<Skeleton className="h-3 w-16" />
									<Skeleton className="h-3 w-20" />
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-8">
 
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold text-foreground">
						Olá, {user?.name} 👋
					</h1>
					<p className="text-muted-foreground mt-2">
						Explore Seus Conteúdos
					</p>
				</div>
				<Badge className="bg-green-100 flex items-center text-blue-900 border-green-200 px-4 py-2 text-sm">
					<GraduationCapIcon className="h-4 w-4 mr-2" />
					ALUNO
				</Badge>
			</div>

		 

 
		{accessibleContent.length > 0 && (
			<div className="space-y-6">
				 

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{accessibleContent.map((item, index) => (
						<motion.div
							key={item.id}
							initial={{ opacity: 0, y: 20 }}
							animate={{ opacity: 1, y: 0 }}
							transition={{ delay: index * 0.1 }}
						>
							<Card
								className="overflow-hidden hover:shadow-lg transition-all duration-300 cursor-pointer group"
								onClick={() => handleContentClick(item)}
							>
					 
								<div className="relative h-48">
									{item.image ? (
										<img
											src={item.image}
											alt={item.title}
											className="w-full h-full object-cover"
										/>
									) : (
										<div className="w-full h-full bg-gradient-to-br from-blue-500 to-purple-600 flex items-center justify-center">
											<BookOpenIcon className="h-16 w-16 text-white opacity-50" />
										</div>
									)}
								</div>

						 
								<CardContent className="p-6">
									<div className="space-y-3">
										<h3 className="font-semibold text-lg line-clamp-2 group-hover:text-primary transition-colors">
											{item.title}
										</h3>

										<p className="text-muted-foreground text-sm line-clamp-3">
											{item.description}
										</p>
									</div>
								</CardContent>
							</Card>
						</motion.div>
					))}
				</div>
			</div>
		)}
		</div>
	);
}
