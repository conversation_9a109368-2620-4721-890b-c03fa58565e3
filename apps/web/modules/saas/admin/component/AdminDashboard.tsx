"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@ui/components/card";
import { Button } from "@ui/components/button";
import { Badge } from "@ui/components/badge";
import {
	GraduationCapIcon,
	UsersIcon,
	Building2Icon,
	SettingsIcon,
	ShieldIcon,
	TrendingUpIcon,
	CheckCircleIcon,
	ArrowRightIcon,
	ActivityIcon,
	DatabaseIcon,
} from "lucide-react";
import Link from "next/link";
import { useSession } from "@saas/auth/hooks/use-session";

export function AdminDashboard() {
	const { user } = useSession();

	const adminActions = [
		{
			title: "Gestão de Cursos",
			description: "Criar e gerenciar cursos de todas as organizações",
			href: "/app/admin/courses",
			icon: GraduationCapIcon,
			color: "blue",
			stats: "3 Cursos",
		},
		{
			title: "Membros",
			description: "Gerenciar usuários e suas permissões",
			href: "/app/admin/users",
			icon: UsersIcon,
			color: "green",
			stats: "935 Usuários",
		},
		{
			title: "Workspaces",
			description: "Gerenciar organizações e configurações",
			href: "/app/admin/organizations",
			icon: Building2Icon,
			color: "purple",
			stats: "3 Organizações",
		},
	];

	const quickStats = [
		{
			title: "Total de Usuários",
			value: "935",
			change: "+12%",
			trend: "up",
			icon: UsersIcon,
		},
		{
			title: "Cursos Ativos",
			value: "3",
			change: "+1",
			trend: "up",
			icon: GraduationCapIcon,
		},
		{
			title: "Organizações",
			value: "3",
			change: "0",
			trend: "neutral",
			icon: Building2Icon,
		},
		{
			title: "Taxa de Atividade",
			value: "89%",
			change: "+5%",
			trend: "up",
			icon: ActivityIcon,
		},
	];

	const getColorClasses = (color: string) => {
		switch (color) {
			case "blue":
				return {
					bg: "bg-blue-100 dark:bg-blue-900/30",
					hoverBg: "group-hover:bg-blue-200 dark:group-hover:bg-blue-900/50",
					iconColor: "text-blue-600 dark:text-blue-400",
				};
			case "green":
				return {
					bg: "bg-green-100 dark:bg-green-900/30",
					hoverBg: "group-hover:bg-green-200 dark:group-hover:bg-green-900/50",
					iconColor: "text-green-600 dark:text-green-400",
				};
			case "purple":
				return {
					bg: "bg-purple-100 dark:bg-purple-900/30",
					hoverBg: "group-hover:bg-purple-200 dark:group-hover:bg-purple-900/50",
					iconColor: "text-purple-600 dark:text-purple-400",
				};
			default:
				return {
					bg: "bg-gray-100 dark:bg-gray-900/30",
					hoverBg: "group-hover:bg-gray-200 dark:group-hover:bg-gray-900/50",
					iconColor: "text-gray-600 dark:text-gray-400",
				};
		}
	};

	return (
		<div className="space-y-8">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold text-foreground">
					Olá, {user?.name} 👋
					</h1>
					<p className="text-muted-foreground mt-2">
						 Gerencie sua Plataforma de Membros.
					</p>
				</div>
				<Badge className="bg-primary/10 flex items-center text-primary border-primary/20 px-4 py-2 text-sm">
					<ShieldIcon className="h-4 w-4 mr-2" />
					ADMINISTRADOR
				</Badge>
			</div>

			{/* Quick Stats */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
				{quickStats.map((stat) => (
					<Card key={stat.title} className="hover:shadow-md transition-shadow">
						<CardContent className="p-6">
							<div className="flex items-center justify-between">
								<div>
									<p className="text-sm font-medium text-muted-foreground">
										{stat.title}
									</p>
									<div className="flex items-center gap-2 mt-2">
										<p className="text-2xl font-bold text-foreground">
											{stat.value}
										</p>
										{stat.trend === "up" && (
											<Badge status="success" className="text-green-600 flex items-center bg-green-100 dark:bg-green-900/30">
												<TrendingUpIcon className="h-3 w-3 mr-1" />
												{stat.change}
											</Badge>
										)}
									</div>
								</div>
								<div className="p-3 rounded-full bg-primary/10">
									<stat.icon className="h-6 w-6 text-primary" />
								</div>
							</div>
						</CardContent>
					</Card>
				))}
			</div>

			{/* Main Actions */}
				<div>
					<h2 className="text-xl text-foreground">
						Ações Administrativas
					</h2>
					<p className="text-muted-foreground">
						Acesse rapidamente as principais funcionalidades do sistema
					</p>
				</div>
				<div>
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
						{adminActions.map((action) => {
							const colorClasses = getColorClasses(action.color);
							return (
								<Link key={action.href} href={action.href} className="group">
									<Card className="h-full transition-all duration-200 hover:shadow-lg hover:-translate-y-1 border-border/50 group-hover:border-primary/30">
										<CardContent className="p-6">
											<div className="flex items-start justify-between mb-4">
												<div className={`p-3 rounded-full ${colorClasses.bg} ${colorClasses.hoverBg} transition-colors`}>
													<action.icon className={`h-6 w-6 ${colorClasses.iconColor}`} />
												</div>
												<ArrowRightIcon className="h-5 w-5 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
											</div>
											<div className="space-y-2">
												<h3 className="font-semibold text-foreground group-hover:text-primary transition-colors">
													{action.title}
												</h3>
												<p className="text-sm text-muted-foreground">
													{action.description}
												</p>
												<div className="flex items-center justify-between mt-4">
													<Badge status="info" className="text-xs">
														{action.stats}
													</Badge>
												</div>
											</div>
										</CardContent>
									</Card>
								</Link>
							);
						})}
					</div>
				</div>


		</div>
	);
}
